import { useState, useEffect, useMemo } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>HeaderActions, EmptyStates } from '../components/shared'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { <PERSON><PERSON> } from '../components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '../components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select'
import { Input } from '../components/ui/input'
import InboxItem, { type InboxItem as InboxItemType } from '../components/features/InboxItem'
import QuickCaptureDialog from '../components/features/QuickCaptureDialog'
import TaskList from '../components/features/TaskList'
import CreateTaskDialog from '../components/features/CreateTaskDialog'
import CreateProjectDialog from '../components/features/CreateProjectDialog'
import CreateAreaDialog from '../components/features/CreateAreaDialog'
import CreateHabitDialog from '../components/features/CreateHabitDialog'
import CreateRecurringTaskDialog from '../components/features/CreateRecurringTaskDialog'
import UniversalKPIDialog from '../components/common/UniversalKPIDialog'
import { useLanguage } from '../contexts/LanguageContext'
import { useTaskStore } from '../store/taskStore'
import { useProjectStore } from '../store/projectStore'
import { useAreaStore } from '../store/areaStore'
import { useUIStore } from '../store/uiStore'
import { useUserSettingsStore } from '../store/userSettingsStore'
import { databaseApi, fileSystemApi } from '../lib/api'
import type { ExtendedTask, Project, Area } from '../../../shared/types'

// Create a custom hook for inbox state management with persistence
const useInboxState = () => {
  const [items, setItems] = useState<InboxItemType[]>(() => {
    // Try to load from localStorage on initialization
    const saved = localStorage.getItem('paolife-inbox-items')
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        console.log('🔧 [DEBUG] Loaded inbox items from localStorage:', parsed.length, 'items')
        return parsed
      } catch (error) {
        console.error('🔧 [DEBUG] Failed to parse saved inbox items:', error)
      }
    }
    console.log('🔧 [DEBUG] No saved inbox items found, starting with empty array')
    return []
  })

  // Save to localStorage whenever items change
  useEffect(() => {
    console.log('🔧 [DEBUG] Saving inbox items to localStorage:', items.length, 'items')
    localStorage.setItem('paolife-inbox-items', JSON.stringify(items))
  }, [items])

  return [items, setItems] as const
}

export function InboxPage() {
  const [items, setItems] = useInboxState()
  const [isQuickCaptureOpen, setIsQuickCaptureOpen] = useState(false)
  const [isCreateTaskDialogOpen, setIsCreateTaskDialogOpen] = useState(false)
  const [isCreateProjectDialogOpen, setIsCreateProjectDialogOpen] = useState(false)
  const [isCreateAreaDialogOpen, setIsCreateAreaDialogOpen] = useState(false)
  const [currentCreateDialog, setCurrentCreateDialog] = useState<{
    type: 'task' | 'kpi' | 'habit' | 'maintenance' | 'checklist'
    data: any
  } | null>(null)
  const [isCreateKPIDialogOpen, setIsCreateKPIDialogOpen] = useState(false)
  const [isCreateHabitDialogOpen, setIsCreateHabitDialogOpen] = useState(false)
  const [isCreateMaintenanceDialogOpen, setIsCreateMaintenanceDialogOpen] = useState(false)
  const [filterType, setFilterType] = useState<string>('all')
  const [filterProcessed, setFilterProcessed] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [pendingCreateData, setPendingCreateData] = useState<{
    itemId: string
    type: 'project' | 'area' | 'task' | 'kpi' | 'habit' | 'maintenance' | 'checklist'
    data: any
    targetId?: string
    targetType?: 'project' | 'area'
  } | null>(null)
  const { t } = useLanguage()

  // Store hooks
  const { tasks, addTask, updateTask, deleteTask, moveTask } = useTaskStore()
  const { projects, addProject, fetchProjects } = useProjectStore()
  const { areas, addArea, fetchAreas } = useAreaStore()
  const { addNotification } = useUIStore()

  // Get unassigned tasks (no project and no area)
  const unassignedTasks = useMemo(() => {
    return tasks.filter((task) => !task.projectId && !task.areaId)
  }, [tasks])

  // 获取项目和领域数据用于选择器
  const availableProjects = useMemo(() => {
    return projects.filter(p => !p.archived).map(p => ({ id: p.id, name: p.name }))
  }, [projects])

  const availableAreas = useMemo(() => {
    return areas.filter(a => !a.archived).map(a => ({ id: a.id, name: a.name }))
  }, [areas])

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      // 加载项目和领域数据
      await Promise.all([
        fetchProjects(),
        fetchAreas()
      ])
    }

    loadData()
  }, [])

  // Add effect to log when component mounts/unmounts
  useEffect(() => {
    console.log('🔧 [DEBUG] InboxPage component mounted')
    return () => {
      console.log('🔧 [DEBUG] InboxPage component unmounted')
    }
  }, [])

  const handleCreateItem = (itemData: Omit<InboxItemType, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newItem: InboxItemType = {
      ...itemData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    console.log('🔧 [DEBUG] Adding new item to inbox:', newItem)
    setItems((prev) => [newItem, ...prev])
    setIsQuickCaptureOpen(false)

    addNotification({
      type: 'success',
      title: '内容已捕获',
      message: '已添加到收件箱'
    })
  }

  const handleEditItem = (updatedItem: InboxItemType) => {
    setItems((prev) => prev.map((item) => (item.id === updatedItem.id ? updatedItem : item)))
  }

  const handleDeleteItem = (itemId: string) => {
    if (confirm(t('pages.inbox.dialogs.confirmDeleteItem'))) {
      setItems((prev) => prev.filter((item) => item.id !== itemId))
    }
  }

  // 处理创建新实体
  const handleProcessCreate = async (
    itemId: string,
    type: 'project' | 'area' | 'resource',
    data: any
  ) => {
    if (type === 'project' || type === 'area') {
      // 设置待创建数据并打开对话框
      setPendingCreateData({ itemId, type, data })
      if (type === 'project') {
        setIsCreateProjectDialogOpen(true)
      } else {
        setIsCreateAreaDialogOpen(true)
      }
    } else if (type === 'resource') {
      // 资源直接创建
      console.log('🔧 [DEBUG] Creating resource with data:', { itemId, data })

      try {
        // TODO: 实现资源创建API
        const newResource = {
          id: `resource-${Date.now()}`,
          title: data.name,
          description: data.description,
          type: data.description.includes('http') ? 'link' : 'note'
        }

        console.log('🔧 [DEBUG] Created resource object:', newResource)

        // 更新收件箱状态
        setItems((prev) => {
          const updated = prev.map((item) =>
            item.id === itemId
              ? {
                  ...item,
                  processed: true,
                  processedAt: new Date().toISOString(),
                  processedTo: {
                    type: 'resource',
                    id: newResource.id,
                    name: newResource.title
                  },
                  updatedAt: new Date().toISOString()
                }
              : item
          )
          console.log('🔧 [DEBUG] Updated items after resource creation:', {
            itemId,
            updatedItem: updated.find(i => i.id === itemId),
            totalItems: updated.length,
            processedItems: updated.filter(i => i.processed).length
          })
          return updated
        })

        // 创建MD文件
        const today = new Date().toISOString().split('T')[0]
        const sequence = Date.now().toString().slice(-4) // 取时间戳后4位作为序号
        const fileName = `收件箱${today}-${sequence}.md`
        const fileContent = items.find(i => i.id === itemId)?.content || ''

        console.log('🔧 [DEBUG] Creating MD file:', { fileName, fileContent })

        // 获取资源目录路径
        const { settings } = useUserSettingsStore.getState()
        let resourcesPath: string
        if (settings.workspaceDirectory) {
          resourcesPath = `${settings.workspaceDirectory}/PaoLife`
        } else {
          const userDataPath = await window.electronAPI.app.getPath('userData')
          resourcesPath = `${userDataPath}/resources`
        }

        const filePath = `${resourcesPath}/${fileName}`

        // 创建MD文件内容
        const defaultContent = `# ${newResource.title}\n\n${fileContent}\n\n创建时间：${new Date().toLocaleString()}\n`

        // 调用文件系统API创建MD文件
        const fileResult = await fileSystemApi.writeFile({
          path: filePath,
          content: defaultContent,
          createDirs: true
        })

        if (!fileResult.success) {
          throw new Error(fileResult.error || '创建文件失败')
        }

        // 调用资源创建API将资源添加到资源库
        const resourceResult = await databaseApi.createResource({
          resourcePath: `/${fileName}`, // 相对于资源目录的路径
          title: newResource.title,
          projectId: undefined,
          areaId: undefined
        })

        if (!resourceResult.success) {
          throw new Error(resourceResult.error || '添加资源到数据库失败')
        }

        addNotification({
          type: 'success',
          title: '资源创建成功',
          message: `已创建资源文件"${fileName}"并添加到资源库`
        })
      } catch (error) {
        console.error('🔧 [DEBUG] Failed to create resource:', error)
        addNotification({
          type: 'error',
          title: '创建失败',
          message: '创建资源时发生错误'
        })
      }
    }
  }

  // 处理关联到现有实体
  const handleProcessLink = async (
    itemId: string,
    type: 'project' | 'area',
    targetId: string,
    actionType: string
  ) => {
    const item = items.find(i => i.id === itemId)
    if (!item) return

    try {
      // 根据动作类型设置待创建数据
      const title = item.content.split('\n')[0]
      const extractedTitle = title.length > 50 ? title.slice(0, 47) + '...' : title

      let createData = null
      let dialogType = ''

      switch (actionType) {
        case 'task':
          createData = {
            title: extractedTitle,
            description: item.content,
            priority: item.priority,
            projectId: type === 'project' ? targetId : undefined,
            areaId: type === 'area' ? targetId : undefined
          }
          dialogType = 'task'
          break
        case 'kpi':
          createData = {
            name: extractedTitle,
            value: '0',
            target: '100',
            unit: '',
            frequency: 'monthly',
            direction: 'increase',
            projectId: type === 'project' ? targetId : undefined,
            areaId: type === 'area' ? targetId : undefined
          }
          dialogType = 'kpi'
          break
        case 'habit':
          createData = {
            name: extractedTitle,
            description: item.content,
            targetFrequency: 7,
            areaId: targetId
          }
          dialogType = 'habit'
          break
        case 'maintenance':
          createData = {
            title: extractedTitle,
            description: item.content,
            frequency: 'weekly',
            areaId: targetId
          }
          dialogType = 'maintenance'
          break
        case 'checklist':
          createData = {
            name: extractedTitle,
            description: item.content,
            areaId: targetId
          }
          dialogType = 'checklist'
          break
      }

      // 设置待创建数据并打开相应对话框
      setPendingCreateData({
        itemId,
        type: dialogType as any,
        data: createData,
        targetId,
        targetType: type
      })

      // 这里需要根据不同的类型打开不同的对话框
      // 暂时先直接创建任务作为示例
      if (actionType === 'task') {
        const newTask: ExtendedTask = {
          id: `task-${Date.now()}`,
          title: extractedTitle,
          description: item.content,
          completed: false,
          priority: item.priority as any,
          projectId: type === 'project' ? targetId : undefined,
          areaId: type === 'area' ? targetId : undefined,
          createdAt: new Date(),
          updatedAt: new Date()
        }
        addTask(newTask)

        // 获取目标实体名称
        const targetName = type === 'project'
          ? projects.find(p => p.id === targetId)?.name || 'Unknown Project'
          : areas.find(a => a.id === targetId)?.name || 'Unknown Area'

        // 更新收件箱状态
        setItems((prev) =>
          prev.map((i) =>
            i.id === itemId
              ? {
                  ...i,
                  processed: true,
                  processedAt: new Date().toISOString(),
                  processedTo: {
                    type,
                    id: targetId,
                    name: targetName
                  },
                  updatedAt: new Date().toISOString()
                }
              : i
          )
        )

        addNotification({
          type: 'success',
          title: '任务创建成功',
          message: `已在"${targetName}"中创建任务"${extractedTitle}"`
        })
      } else {
        // 其他类型暂时显示提示
        addNotification({
          type: 'info',
          title: '功能开发中',
          message: `${actionType} 创建功能正在开发中`
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: '创建失败',
        message: '创建内容时发生错误'
      })
    }
  }

  // 处理打开创建对话框
  const handleOpenCreateDialog = (
    type: 'task' | 'kpi' | 'habit' | 'maintenance' | 'checklist',
    data: any,
    itemId: string
  ) => {
    console.log('🔧 [DEBUG] handleOpenCreateDialog called with:', {
      type,
      data,
      itemId,
      pendingCreateData
    })

    // 从data中提取目标信息并设置到pendingCreateData
    const targetType = data.projectId ? 'project' : 'area'
    const targetId = data.projectId || data.areaId

    console.log('🔧 [DEBUG] Setting pendingCreateData:', {
      targetType,
      targetId,
      itemId
    })

    // 设置待处理数据
    setPendingCreateData({
      itemId,
      type: type as any,
      data,
      targetId,
      targetType: targetType as any
    })

    setCurrentCreateDialog({ type, data })

    // 根据类型打开对应的对话框
    switch (type) {
      case 'task':
        console.log('🔧 [DEBUG] Opening task dialog')
        setIsCreateTaskDialogOpen(true)
        break
      case 'kpi':
        console.log('🔧 [DEBUG] Opening KPI dialog')
        setIsCreateKPIDialogOpen(true)
        break
      case 'habit':
        console.log('🔧 [DEBUG] Opening habit dialog')
        setIsCreateHabitDialogOpen(true)
        break
      case 'maintenance':
        console.log('🔧 [DEBUG] Opening maintenance dialog')
        setIsCreateMaintenanceDialogOpen(true)
        break
      case 'checklist':
        // TODO: 打开清单创建对话框
        addNotification({
          type: 'info',
          title: '功能开发中',
          message: '清单创建功能正在开发中'
        })
        break
    }
  }

  // 处理KPI创建 - 使用KPI管理器
  const handleCreateKPI = async (data: any) => {
    console.log('🔧 [DEBUG] handleCreateKPI called with:', {
      data,
      pendingCreateData,
      currentCreateDialog
    })

    try {
      const targetType = pendingCreateData?.targetType
      const targetId = pendingCreateData?.targetId

      console.log('🔧 [DEBUG] KPI creation params:', {
        targetType,
        targetId,
        hasTargetId: !!targetId
      })

      if (!targetId) {
        console.error('🔧 [DEBUG] Target ID is missing!')
        throw new Error('Target ID is required')
      }

      // 使用KPI管理器创建
      if (targetType === 'project') {
        console.log('🔧 [DEBUG] Creating project KPI...')
        const { createProjectKPIManager } = await import('../lib/kpiApiAdapters')
        const kpiManager = createProjectKPIManager()
        await kpiManager.create(targetId, data)
        console.log('🔧 [DEBUG] Project KPI created successfully')
      } else if (targetType === 'area') {
        console.log('🔧 [DEBUG] Creating area metric...')
        const { createAreaMetricManager } = await import('../lib/kpiApiAdapters')
        const kpiManager = createAreaMetricManager()
        await kpiManager.create(targetId, data)
        console.log('🔧 [DEBUG] Area metric created successfully')
      }

      // 更新收件箱状态
      const itemId = pendingCreateData?.itemId
      if (itemId) {
        const targetName = targetType === 'project'
          ? projects.find(p => p.id === targetId)?.name || 'Unknown Project'
          : areas.find(a => a.id === targetId)?.name || 'Unknown Area'

        console.log('🔧 [DEBUG] Updating inbox item:', { itemId, targetName })

        setItems((prev) => {
          const updated = prev.map((item) =>
            item.id === itemId
              ? {
                  ...item,
                  processed: true,
                  processedAt: new Date().toISOString(),
                  processedTo: {
                    type: targetType,
                    id: targetId,
                    name: targetName
                  },
                  updatedAt: new Date().toISOString()
                }
              : item
          )
          console.log('🔧 [DEBUG] Updated items after KPI creation:', {
            itemId,
            updatedItem: updated.find(i => i.id === itemId),
            totalItems: updated.length,
            processedItems: updated.filter(i => i.processed).length
          })
          return updated
        })

        addNotification({
          type: 'success',
          title: 'KPI创建成功',
          message: `已在"${targetName}"中创建KPI"${data.name}"`
        })
      }
    } catch (error) {
      console.error('🔧 [DEBUG] Failed to create KPI:', error)
      addNotification({
        type: 'error',
        title: 'KPI创建失败',
        message: error instanceof Error ? error.message : '创建KPI时发生错误'
      })
    } finally {
      setCurrentCreateDialog(null)
    }
  }

  // 处理习惯创建
  const handleCreateHabit = async (data: any) => {
    console.log('🔧 [DEBUG] handleCreateHabit called with:', {
      data,
      pendingCreateData,
      currentCreateDialog
    })

    try {
      const targetId = pendingCreateData?.targetId

      console.log('🔧 [DEBUG] Habit creation params:', {
        targetId,
        hasTargetId: !!targetId,
        dataKeys: Object.keys(data || {})
      })

      if (!targetId) {
        console.error('🔧 [DEBUG] Area ID is missing!')
        throw new Error('Area ID is required')
      }

      const habitData = {
        name: data.name,
        areaId: targetId,
        description: data.description,
        frequency: data.frequency || 'daily',
        target: data.target || 1
      }

      console.log('🔧 [DEBUG] Creating habit with data:', habitData)

      const result = await databaseApi.createHabit(habitData)

      console.log('🔧 [DEBUG] Habit creation result:', result)

      if (result?.success) {
        // 更新收件箱状态
        const itemId = pendingCreateData?.itemId
        if (itemId) {
          const targetName = areas.find(a => a.id === targetId)?.name || 'Unknown Area'

          console.log('🔧 [DEBUG] Updating inbox item for habit:', { itemId, targetName })

          setItems((prev) => {
            const updated = prev.map((item) =>
              item.id === itemId
                ? {
                    ...item,
                    processed: true,
                    processedAt: new Date().toISOString(),
                    processedTo: {
                      type: 'area',
                      id: targetId,
                      name: targetName
                    },
                    updatedAt: new Date().toISOString()
                  }
                : item
            )
            console.log('🔧 [DEBUG] Updated items after habit creation:', {
              itemId,
              updatedItem: updated.find(i => i.id === itemId),
              totalItems: updated.length,
              processedItems: updated.filter(i => i.processed).length
            })
            return updated
          })

          addNotification({
            type: 'success',
            title: '习惯创建成功',
            message: `已在"${targetName}"中创建习惯"${data.name}"`
          })
        }
      } else {
        throw new Error(result?.error || 'Failed to create habit')
      }
    } catch (error) {
      console.error('🔧 [DEBUG] Failed to create habit:', error)
      addNotification({
        type: 'error',
        title: '习惯创建失败',
        message: error instanceof Error ? error.message : '创建习惯时发生错误'
      })
    } finally {
      setCurrentCreateDialog(null)
    }
  }



  // 处理直接处理
  const handleProcessItem = (
    itemId: string,
    destination: { type: 'project' | 'area' | 'resource' | 'processed'; id: string; name: string }
  ) => {
    setItems((prev) =>
      prev.map((item) =>
        item.id === itemId
          ? {
              ...item,
              processed: true,
              processedAt: new Date().toISOString(),
              processedTo: destination,
              updatedAt: new Date().toISOString()
            }
          : item
      )
    )
  }

  const handleToggleProcessed = (itemId: string) => {
    // 只允许取消处理（unprocess），不允许直接标记为已处理
    setItems((prev) =>
      prev.map((item) =>
        item.id === itemId && item.processed
          ? {
              ...item,
              processed: false,
              processedAt: undefined,
              processedTo: undefined,
              updatedAt: new Date().toISOString()
            }
          : item
      )
    )
  }

  // Task handlers
  const handleCreateTask = async (
    taskData: Omit<ExtendedTask, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    const newTask: ExtendedTask = {
      ...taskData,
      id: `task-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    addTask(newTask)

    // 如果是从收件箱创建的任务，标记收件箱条目为已处理
    if (currentCreateDialog) {
      const itemId = pendingCreateData?.itemId
      if (itemId) {
        setItems((prev) =>
          prev.map((item) =>
            item.id === itemId
              ? {
                  ...item,
                  processed: true,
                  processedAt: new Date().toISOString(),
                  processedTo: {
                    type: newTask.projectId ? 'project' : 'area',
                    id: newTask.projectId || newTask.areaId || '',
                    name: newTask.projectId
                      ? projects.find(p => p.id === newTask.projectId)?.name || 'Unknown Project'
                      : areas.find(a => a.id === newTask.areaId)?.name || 'Unknown Area'
                  },
                  updatedAt: new Date().toISOString()
                }
              : item
          )
        )

        addNotification({
          type: 'success',
          title: '任务创建成功',
          message: `已创建任务"${newTask.title}"`
        })
      }
      setCurrentCreateDialog(null)
    }
  }

  const handleToggleTask = (taskId: string, completed: boolean) => {
    updateTask(taskId, { completed })
  }

  const handleEditTask = (task: ExtendedTask) => {
    // TODO: Implement task editing
    console.log('Edit task:', task)
  }

  const handleDeleteTask = (taskId: string) => {
    deleteTask(taskId)
  }

  const handleTaskMove = (taskId: string, newParentId?: string, newIndex?: number) => {
    moveTask(taskId, newParentId, newIndex)
  }

  // 处理项目创建确认
  const handleCreateProject = async (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!pendingCreateData || pendingCreateData.type !== 'project') return

    try {
      const result = await databaseApi.createProject({
        name: projectData.name,
        description: projectData.description,
        goal: projectData.goal,
        areaId: projectData.areaId
      })

      if (result.success) {
        // 添加到store
        addProject(result.data)

        // 更新收件箱状态
        setItems((prev) =>
          prev.map((item) =>
            item.id === pendingCreateData.itemId
              ? {
                  ...item,
                  processed: true,
                  processedAt: new Date().toISOString(),
                  processedTo: {
                    type: 'project',
                    id: result.data.id,
                    name: result.data.name
                  },
                  updatedAt: new Date().toISOString()
                }
              : item
          )
        )

        addNotification({
          type: 'success',
          title: '项目创建成功',
          message: `已创建项目"${result.data.name}"`
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: '创建失败',
        message: error instanceof Error ? error.message : '创建项目时发生错误'
      })
    } finally {
      setPendingCreateData(null)
      setIsCreateProjectDialogOpen(false)
    }
  }

  // 处理领域创建确认
  const handleCreateArea = async (areaData: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!pendingCreateData || pendingCreateData.type !== 'area') return

    try {
      const result = await databaseApi.createArea({
        name: areaData.name,
        description: areaData.description,
        standard: areaData.standard,
        status: areaData.status,
        reviewFrequency: areaData.reviewFrequency
      })

      if (result.success) {
        // 添加到store
        addArea(result.data)

        // 更新收件箱状态
        setItems((prev) =>
          prev.map((item) =>
            item.id === pendingCreateData.itemId
              ? {
                  ...item,
                  processed: true,
                  processedAt: new Date().toISOString(),
                  processedTo: {
                    type: 'area',
                    id: result.data.id,
                    name: result.data.name
                  },
                  updatedAt: new Date().toISOString()
                }
              : item
          )
        )

        addNotification({
          type: 'success',
          title: '领域创建成功',
          message: `已创建领域"${result.data.name}"`
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: '创建失败',
        message: error instanceof Error ? error.message : '创建领域时发生错误'
      })
    } finally {
      setPendingCreateData(null)
      setIsCreateAreaDialogOpen(false)
    }
  }

  // Filter items
  const filteredItems = items.filter((item) => {
    const matchesType = filterType === 'all' || item.type === filterType
    const matchesProcessed =
      filterProcessed === 'all' ||
      (filterProcessed === 'processed' && item.processed) ||
      (filterProcessed === 'unprocessed' && !item.processed)
    const matchesSearch =
      !searchQuery ||
      item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    return matchesType && matchesProcessed && matchesSearch
  })

  console.log('🔧 [DEBUG] Filter results:', {
    totalItems: items.length,
    filteredItems: filteredItems.length,
    filterType,
    filterProcessed,
    searchQuery,
    itemsWithProcessedStatus: items.map(i => ({ id: i.id, processed: i.processed }))
  })

  const unprocessedCount = items.filter((item) => !item.processed).length

  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader
        title={t('pages.inbox.title')}
        description={t('pages.inbox.description')}
        badge={{
          text:
            unprocessedCount > 0
              ? `${unprocessedCount} ${t('pages.inbox.unprocessed')}`
              : t('pages.inbox.allProcessed'),
          variant: unprocessedCount > 0 ? 'secondary' : 'default'
        }}
        actions={
          <PageHeaderActions.Create onClick={() => setIsQuickCaptureOpen(true)}>
            {t('pages.inbox.quickCapture')}
          </PageHeaderActions.Create>
        }
      />

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder={t('pages.inbox.searchPlaceholder')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>

        <div className="flex gap-2">
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder={t('pages.inbox.filters.typeLabel')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('pages.inbox.filters.type.allTypes')}</SelectItem>
              <SelectItem value="note">📝 {t('pages.inbox.filters.type.notes')}</SelectItem>
              <SelectItem value="task">✅ {t('pages.inbox.filters.type.tasks')}</SelectItem>
              <SelectItem value="idea">💡 {t('pages.inbox.filters.type.ideas')}</SelectItem>
              <SelectItem value="link">🔗 {t('pages.inbox.filters.type.links')}</SelectItem>
              <SelectItem value="file">📄 {t('pages.inbox.filters.type.files')}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterProcessed} onValueChange={setFilterProcessed}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder={t('pages.inbox.filters.statusLabel')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('pages.inbox.filters.status.allItems')}</SelectItem>
              <SelectItem value="unprocessed">
                {t('pages.inbox.filters.status.unprocessed')}
              </SelectItem>
              <SelectItem value="processed">{t('pages.inbox.filters.status.processed')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Tabs for Processed and Unprocessed Items */}
      <Tabs defaultValue="unprocessed" className="space-y-4">
        <TabsList>
          <TabsTrigger value="unprocessed">未处理 ({items.filter(item => !item.processed).length})</TabsTrigger>
          <TabsTrigger value="processed">已处理 ({items.filter(item => item.processed).length})</TabsTrigger>
        </TabsList>

        <TabsContent value="unprocessed">
          <Card>
            <CardHeader>
              <CardTitle>未处理项目</CardTitle>
              <CardDescription>需要分类处理的收件箱内容</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    <span>加载中...</span>
                  </div>
                </div>
              ) : filteredItems.filter(item => !item.processed).length === 0 ? (
                <div className="max-w-md mx-auto mt-12">
                  <div className="text-center py-8 text-muted-foreground">
                    <div className="text-4xl mb-2">✅</div>
                    <p className="text-sm">所有项目都已处理完成</p>
                    <p className="text-xs mt-1">收件箱已清空</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredItems.filter(item => !item.processed).map((item) => (
                    <InboxItem
                      key={item.id}
                      item={item}
                      onEdit={handleEditItem}
                      onDelete={handleDeleteItem}
                      onProcess={handleProcessItem}
                      onProcessCreate={handleProcessCreate}
                      onProcessLink={handleProcessLink}
                      onOpenCreateDialog={handleOpenCreateDialog}
                      onToggleProcessed={handleToggleProcessed}
                      projects={availableProjects}
                      areas={availableAreas}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="processed">
          <Card>
            <CardHeader>
              <CardTitle>已处理项目</CardTitle>
              <CardDescription>已经分类处理的收件箱内容</CardDescription>
            </CardHeader>
            <CardContent>
              {(() => {
                const processedItems = filteredItems.filter(item => item.processed)
                console.log('🔧 [DEBUG] Processed tab - filtered items:', {
                  totalItems: items.length,
                  filteredItems: filteredItems.length,
                  processedItems: processedItems.length,
                  processedItemIds: processedItems.map(i => i.id)
                })
                return processedItems.length === 0 ? (
                  <div className="max-w-md mx-auto mt-12">
                    <div className="text-center py-8 text-muted-foreground">
                      <div className="text-4xl mb-2">📥</div>
                      <p className="text-sm">暂无已处理的项目</p>
                      <p className="text-xs mt-1">处理收件箱内容后会显示在这里</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {processedItems.map((item) => (
                      <InboxItem
                        key={item.id}
                        item={item}
                        onEdit={handleEditItem}
                        onDelete={handleDeleteItem}
                        onProcess={handleProcessItem}
                        onProcessCreate={handleProcessCreate}
                        onProcessLink={handleProcessLink}
                        onOpenCreateDialog={handleOpenCreateDialog}
                        onToggleProcessed={handleToggleProcessed}
                        projects={availableProjects}
                        areas={availableAreas}
                      />
                    ))}
                  </div>
                )
              })()}
            </CardContent>
          </Card>
        </TabsContent>

      </Tabs>

      {/* Quick Capture Dialog */}
      <QuickCaptureDialog
        isOpen={isQuickCaptureOpen}
        onClose={() => setIsQuickCaptureOpen(false)}
        onSubmit={handleCreateItem}
      />

      {/* Create Task Dialog */}
      <CreateTaskDialog
        isOpen={isCreateTaskDialogOpen}
        onClose={() => {
          setIsCreateTaskDialogOpen(false)
          setCurrentCreateDialog(null)
        }}
        onSubmit={handleCreateTask}
        initialData={currentCreateDialog?.type === 'task' ? currentCreateDialog.data : undefined}
      />

      {/* 项目创建对话框 */}
      <CreateProjectDialog
        isOpen={isCreateProjectDialogOpen}
        onClose={() => {
          setIsCreateProjectDialogOpen(false)
          setPendingCreateData(null)
        }}
        onSubmit={handleCreateProject}
        initialData={pendingCreateData?.data}
      />

      {/* 领域创建对话框 */}
      <CreateAreaDialog
        isOpen={isCreateAreaDialogOpen}
        onClose={() => {
          setIsCreateAreaDialogOpen(false)
          setPendingCreateData(null)
        }}
        onSubmit={handleCreateArea}
        initialData={pendingCreateData?.data}
      />

      {/* KPI创建对话框 */}
      <UniversalKPIDialog
        open={isCreateKPIDialogOpen}
        onOpenChange={setIsCreateKPIDialogOpen}
        onSubmit={handleCreateKPI}
        type={pendingCreateData?.targetType || 'project'}
        entityId={pendingCreateData?.targetId || ''}
        initialData={currentCreateDialog?.type === 'kpi' ? currentCreateDialog.data : undefined}
      />

      {/* 习惯创建对话框 */}
      <CreateHabitDialog
        isOpen={isCreateHabitDialogOpen}
        onClose={() => {
          setIsCreateHabitDialogOpen(false)
          setCurrentCreateDialog(null)
        }}
        onSubmit={handleCreateHabit}
        areaId={pendingCreateData?.targetId || ''}
        initialData={currentCreateDialog?.type === 'habit' ? currentCreateDialog.data : undefined}
      />

      {/* 维护任务创建对话框 */}
      <CreateRecurringTaskDialog
        isOpen={isCreateMaintenanceDialogOpen}
        onClose={() => {
          setIsCreateMaintenanceDialogOpen(false)
          setCurrentCreateDialog(null)
        }}
        areaId={pendingCreateData?.targetId || ''}
        onTaskCreated={() => {
          console.log('🔧 [DEBUG] Maintenance task created callback')
          // 任务创建成功的处理
          const itemId = pendingCreateData?.itemId
          const targetId = pendingCreateData?.targetId

          console.log('🔧 [DEBUG] Maintenance task creation params:', {
            itemId,
            targetId,
            pendingCreateData
          })

          if (itemId && targetId) {
            const targetName = areas.find(a => a.id === targetId)?.name || 'Unknown Area'

            console.log('🔧 [DEBUG] Updating inbox item for maintenance task:', { itemId, targetName })

            setItems((prev) => {
              const updated = prev.map((item) =>
                item.id === itemId
                  ? {
                      ...item,
                      processed: true,
                      processedAt: new Date().toISOString(),
                      processedTo: {
                        type: 'area',
                        id: targetId,
                        name: targetName
                      },
                      updatedAt: new Date().toISOString()
                    }
                  : item
              )
              console.log('🔧 [DEBUG] Updated items after maintenance task creation:', {
                itemId,
                updatedItem: updated.find(i => i.id === itemId),
                totalItems: updated.length,
                processedItems: updated.filter(i => i.processed).length
              })
              return updated
            })

            addNotification({
              type: 'success',
              title: '维护任务创建成功',
              message: `已在"${targetName}"中创建维护任务`
            })
          }
          setCurrentCreateDialog(null)
          setIsCreateMaintenanceDialogOpen(false)
        }}
      />
    </div>
  )
}

export default InboxPage
